<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.zhangchuangla</groupId>
    <artifactId>echo-pro</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>chuang-common</module>
        <module>chuang-system</module>
        <module>chuang-framework</module>
        <module>chuang-api</module>
        <module>chuang-quartz</module>
    </modules>


    <properties>
        <app.version>1.0.0</app.version>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.boot.version>3.4.3</spring.boot.version>
        <spring.security.version>6.4.4</spring.security.version>
        <springdoc.version>2.8.3</springdoc.version>
        <mybatis.plus.version>3.5.10</mybatis.plus.version>
        <mysql.version>8.2.0</mysql.version>
        <redis.version>3.2.5</redis.version>
        <thumbnailator.version>0.4.20</thumbnailator.version>
        <aliyun.oss.version>3.17.4</aliyun.oss.version>

        <amazon.awssdk.s3.version>2.20.45</amazon.awssdk.s3.version>
        <minio.version>8.5.17</minio.version>
        <tencent.cos.version>5.6.97</tencent.cos.version>
        <!-- MQ 消息队列相关版本 -->
        <rabbitmq.version>3.4.3</rabbitmq.version>
        <spring.boot.amqp.version>3.4.3</spring.boot.amqp.version>

        <!-- 工具类相关依赖版本 -->
        <lombok.version>1.18.30</lombok.version>
        <jwt.version>0.11.5</jwt.version>
        <okhttp.version>4.12.0</okhttp.version>
        <eu.bitwalker.version>1.21</eu.bitwalker.version>
        <oshi.version>6.6.6</oshi.version>
        <alibaba.fastjson.version>2.0.53</alibaba.fastjson.version>
        <alibaba.druid.starter.version>1.2.24</alibaba.druid.starter.version>
        <apache.commons-io.version>2.18.0</apache.commons-io.version>
        <jaxb.api.version>2.4.0-b180830.0359</jaxb.api.version>
        <javax.activation.version>2.1.2</javax.activation.version>
        <google.gvava.version>32.1.2-jre</google.gvava.version>
        <version.apache.tika>2.9.0</version.apache.tika>
        <apache.velocity>2.4.1</apache.velocity>
        <google.protobuf.version>4.28.2</google.protobuf.version>
        <fast.excel.version>1.2.0</fast.excel.version>
        <apache.poi.version>5.4.0</apache.poi.version>
        <!-- IP 地区转换 -->
        <ip2region.version>2.7.0</ip2region.version>
        <!-- Quartz 定时任务 -->
        <quartz.version>2.5.0</quartz.version>
    </properties>

    <!-- 项目的依赖列表 -->
    <dependencies>
        <!-- Lombok 依赖，仅在编译时启用，用于简化 Java 代码编写 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <!-- 可选依赖，依赖方可以选择不引入 -->
            <optional>true</optional>
        </dependency>
    </dependencies>

    <!-- 依赖管理，用于统一管理依赖的版本 -->
    <dependencyManagement>
        <dependencies>
            <!-- ===================== 项目内模块管理 ===================== -->
            <!-- 公共模块 -->
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-common</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-common-core</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-common-mq</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-common-websocket</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-common-redis</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-common-excel</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-system-monitor</artifactId>
                <version>${app.version}</version>
            </dependency>
            <!-- 框架模块 -->
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-framework</artifactId>
                <version>${app.version}</version>
            </dependency>
            <!-- 系统核心模块 -->
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-system-core</artifactId>
                <version>${app.version}</version>
            </dependency>
            <!-- 系统存储模块 -->
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-system-storage</artifactId>
                <version>${app.version}</version>
            </dependency>
            <!-- 系统消息模块 -->
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-system-message</artifactId>
                <version>${app.version}</version>
            </dependency>
            <!-- 系统模块依赖 -->
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>chuang-system</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.zhangchuangla</groupId>
                <artifactId>chuang-quartz</artifactId>
                <version>${app.version}</version>
            </dependency>

            <!-- ===================== Spring Boot & Security ===================== -->
            <!-- 引入 Spring Boot 依赖管理 POM，统一管理 Spring Boot 相关依赖版本 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Security 相关依赖统一使用指定版本 -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring.security.version}</version>
            </dependency>

            <!-- Spring Boot Web 启动器，用于构建 Web 应用 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <!-- Spring Boot 验证启动器，用于数据验证 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <!-- Spring Boot AOP 启动器，用于面向切面编程 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <!-- Spring Boot Redis 启动器，用于集成 Redis 缓存 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <!-- ===================== JWT & 安全性 ===================== -->
            <!-- JWT API 依赖，用于处理 JWT 相关操作 -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <!-- JWT 实现依赖，运行时使用 -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- JWT Jackson 依赖，用于处理 JWT 中的 JSON 数据，运行时使用 -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- ===================== MyBatis-Plus ===================== -->
            <!-- MyBatis-Plus Spring Boot 3 启动器，简化 MyBatis 开发 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <!-- MyBatis-Plus JSqlParser，提供分页插件支持 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <!-- ===================== 文件存储 & 处理 ===================== -->
            <!-- Thumbnailator 依赖，用于图片处理 -->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnailator.version}</version>
            </dependency>
            <!-- 阿里云 OSS SDK 依赖，用于阿里云对象存储服务 -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.oss.version}</version>
            </dependency>
            <!-- MinIO 客户端依赖，用于 MinIO 对象存储服务 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <!-- 腾讯云 COS 依赖，用于腾讯云对象存储服务 -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${tencent.cos.version}</version>
            </dependency>
            <!-- AWS S3 存储服务依赖 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${amazon.awssdk.s3.version}</version>
            </dependency>
            <!-- IP 转省市区 -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>


            <!-- ===================== 数据库 ===================== -->
            <!-- MySQL 数据库连接驱动 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!-- 阿里巴巴 Druid 数据库连接池 Spring Boot 3 启动器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${alibaba.druid.starter.version}</version>
            </dependency>
            <!-- 阿里巴巴 FastJSON 依赖，用于 JSON 数据处理 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${alibaba.fastjson.version}</version>
            </dependency>
            <!-- ===================== 工具类 ===================== -->
            <!-- OkHttp 依赖，用于 HTTP 请求 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- Apache Tika 核心依赖，用于文件类型检测和内容提取 -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${version.apache.tika}</version>
            </dependency>

            <!-- 用户代理工具类依赖，用于解析用户代理信息 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${eu.bitwalker.version}</version>
            </dependency>
            <!-- OSHI 核心依赖，用于获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
            <!-- Apache Commons IO 依赖，用于文件和流操作 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${apache.commons-io.version}</version>
            </dependency>

            <!-- JAXB API 依赖，用于 XML 数据绑定 -->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.api.version}</version>
            </dependency>
            <!-- Google Guava 依赖，提供常用的工具类 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.gvava.version}</version>
            </dependency>

            <!-- Java 激活框架依赖 -->
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${javax.activation.version}</version>
            </dependency>
            <!-- Spring Boot WebSocket 启动器，用于实现 WebSocket 通信 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <!-- velocity低代码引擎 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${apache.velocity}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- ===================== 项目模块依赖 ===================== -->
            <!-- 通用工具模块依赖 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.18.0</version>
            </dependency>

            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fast.excel.version}</version>
            </dependency>


            <!-- ===================== 安全补丁 ===================== -->
            <!-- Bouncy Castle依赖 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>1.78</version>
            </dependency>
            <!-- Protobuf依赖 -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${google.protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.0</version>
            </dependency>

            <!-- ===================== MQ 消息队列 ===================== -->
            <!-- Spring Boot AMQP 启动器，用于RabbitMQ消息队列 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>${spring.boot.amqp.version}</version>
            </dependency>

            <!-- ===================== Quartz 定时任务 ===================== -->
            <!-- Spring Boot Quartz 启动器，用于定时任务调度 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <!-- Quartz 核心依赖 -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
