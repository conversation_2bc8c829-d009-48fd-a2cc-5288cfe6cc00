package cn.zhangchuangla.common.core.enums;

import lombok.Getter;

/**
 * 数据脱敏类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum MaskingType {

    /**
     * 手机号脱敏：保留前3位和后4位，中间用****代替
     * 示例：18800000000 -> 188****0000
     */
    MOBILE_PHONE("(\\d{3})\\d{4}(\\d{4})", "$1****$2"),

    /**
     * 身份证号脱敏：保留前6位和后4位，中间用****代替
     * 示例：********90******** -> 123456****5678
     */
    ID_CARD("(\\d{6})\\d{8}(\\d{4})", "$1****$2"),

    /**
     * 邮箱脱敏：保留第一个字符和@后面的部分，中间用***代替
     * 示例：<EMAIL> -> t***@example.com
     */
    EMAIL("(\\w)\\w*(@.*)", "$1***$2"),

    /**
     * 姓名脱敏：保留第一个字符，其余用*代替
     * 示例：张三丰 -> 张**
     */
    NAME("(\\S)\\S*", "$1**"),

    /**
     * 银行卡号脱敏：保留前4位和后4位，中间用****代替
     * 示例：622202********90 -> 6222****7890
     */
    BANK_CARD("(\\d{4})\\d*(\\d{4})", "$1****$2"),

    /**
     * 地址脱敏：保留前面部分和最后一个字符，中间用****代替
     * 示例：北京市朝阳区某某街道123号 -> 北京市****3号
     */
    ADDRESS("(.{3}).*(.{2})", "$1****$2"),

    /**
     * 密码脱敏：全部用******代替
     * 示例：password123 -> ******
     */
    PASSWORD(".*", "******"),

    /**
     * 固定电话脱敏：保留区号和后4位，中间用****代替
     * 示例：010-******** -> 010-****5678
     */
    FIXED_PHONE("(\\d{3,4}-?)\\d*(\\d{4})", "$1****$2"),

    /**
     * 自定义脱敏：使用注解中指定的正则表达式和替换规则
     */
    CUSTOM("", ""),

    /**
     * 密钥脱敏 ：保留前3位和后4位，中间用****代替
     */
    SECRET_KEY("(\\w{3})\\w*(\\w{4})", "$1****$2");

    /**
     * 正则表达式
     */
    private final String regex;

    /**
     * 替换字符串
     */
    private final String replacement;

    MaskingType(String regex, String replacement) {
        this.regex = regex;
        this.replacement = replacement;
    }

}
