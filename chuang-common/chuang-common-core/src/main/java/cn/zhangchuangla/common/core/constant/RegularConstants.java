package cn.zhangchuangla.common.core.constant;

/**
 * 正则表达式常量
 *
 * <AUTHOR>
 * <p>
 * created on 2025/4/20 09:05
 */

public interface RegularConstants {

    /**
     * 用户相关正则表达式
     */
    interface User {
        String PASSWORD = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z0-9!@#¥%&*（）——+]{8,20}$";
        String USERNAME = "^[a-zA-Z0-9_]{5,20}$";
        String PHONE = "^1[3-9]\\d{9}$";
        String EMAIL = "\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}";
    }

    /**
     * 存储相关
     * bucketName
     */
    interface Storage {
        String DOMAIN = "^(https?://)?((([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,})|((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(:(\\d+))?(/[^/]*)?$";
        String BUCKET_NAME = "[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]$";
    }
}
