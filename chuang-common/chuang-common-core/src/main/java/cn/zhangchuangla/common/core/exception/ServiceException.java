package cn.zhangchuangla.common.core.exception;

import cn.zhangchuangla.common.core.enums.ResultCode;
import lombok.Getter;

/**
 * <AUTHOR>
 * <p>
 * created on 2025/1/11 10:04
 */
@Getter
public final class ServiceException extends RuntimeException {

    /**
     * 状态码
     */
    private final Integer code;


    public ServiceException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
    }

    public ServiceException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
    }

    public ServiceException(String message) {
        super(message);
        this.code = ResultCode.ERROR.getCode();
    }

}
