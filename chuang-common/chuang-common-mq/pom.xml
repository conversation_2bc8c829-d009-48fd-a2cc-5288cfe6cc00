<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.zhangchuangla</groupId>
        <artifactId>chuang-common</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>chuang-common-mq</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>

        <!-- Spring Boot AMQP 启动器，用于RabbitMQ消息队列 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <!-- 公共核心模块 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-common-core</artifactId>
        </dependency>
    </dependencies>

</project>
