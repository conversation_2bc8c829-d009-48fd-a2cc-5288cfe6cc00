<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.zhangchuangla</groupId>
        <artifactId>chuang-common</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>chuang-common-excel</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- FastExcel -->
        <dependency>
            <groupId>org.dhatim</groupId>
            <artifactId>fastexcel</artifactId>
            <version>0.15.7</version>
        </dependency>

        <!-- FastExcel Reader -->
        <dependency>
            <groupId>org.dhatim</groupId>
            <artifactId>fastexcel-reader</artifactId>
            <version>0.15.7</version>
        </dependency>

        <!-- Redis模块依赖 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-common-redis</artifactId>
        </dependency>

        <!-- 核心模块依赖 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-common-core</artifactId>
        </dependency>

    </dependencies>


</project>
