# MessageQueryService API 文档

## 概述

`MessageQueryService` 是消息查询服务接口，专门负责消息的查询相关操作，包括已读状态的查询和统计。该服务经过优化，提供高性能的消息查询功能。

## 主要功能

- 用户消息列表分页查询（支持已读/未读筛选）
- 用户消息详情查询（自动标记已读）
- 用户消息已读未读统计

## API 接口

### 1. 分页查询用户消息列表

```java
Page<UserMessageDto> listUserMessageList(UserMessageListQueryRequest request)
```

#### 功能描述
分页查询当前用户的消息列表，支持按已读状态筛选，并自动填充每条消息的已读状态。

#### 参数说明
- `request`: 查询参数对象
  - `pageNum`: 页码（必填，≥1）
  - `pageSize`: 每页大小（必填，1-100）
  - `isRead`: 已读状态筛选（可选，true=已读，false=未读，null=全部）
  - `title`: 消息标题模糊查询（可选）
  - `type`: 消息类型（可选，1-系统消息，2-通知消息，3-公告消息）
  - `level`: 消息级别（可选，1-普通，2-重要，3-紧急）
  - `senderName`: 发送者姓名模糊查询（可选）

#### 返回值
- `Page<UserMessageDto>`: 分页结果
  - `current`: 当前页码
  - `size`: 每页大小
  - `total`: 总记录数
  - `records`: 消息列表
    - `id`: 消息ID
    - `title`: 消息标题
    - `content`: 消息内容摘要
    - `isRead`: 已读状态（0-未读，1-已读）
    - `type`: 消息类型
    - `level`: 消息级别
    - `senderName`: 发送者姓名
    - `createTime`: 创建时间

#### 使用示例
```java
// 查询第一页未读消息
UserMessageListQueryRequest request = new UserMessageListQueryRequest();
request.setPageNum(1);
request.setPageSize(20);
request.setIsRead(false);
Page<UserMessageDto> result = messageQueryService.listUserMessageList(request);
```

#### 性能优化
- 使用索引优化查询性能
- 批量获取已读状态，避免N+1查询
- 支持覆盖索引减少回表查询
- 限制单页最大数据量防止性能问题

### 2. 获取用户消息详情

```java
UserMessageVo getUserMessageDetail(Long messageId)
```

#### 功能描述
获取指定消息的详细内容，并自动将消息标记为已读状态。

#### 参数说明
- `messageId`: 消息ID（必填，不能为空）

#### 返回值
- `UserMessageVo`: 消息详情对象
  - `id`: 消息ID
  - `title`: 消息标题
  - `content`: 消息完整内容
  - `type`: 消息类型
  - `level`: 消息级别
  - `senderName`: 发送者姓名
  - `sentTime`: 发送时间

#### 异常情况
- `ServiceException`: 当消息ID为空、消息不存在或用户无权限访问时抛出

#### 使用示例
```java
// 获取消息详情
Long messageId = 123L;
UserMessageVo messageDetail = messageQueryService.getUserMessageDetail(messageId);
```

#### 业务逻辑
1. 验证消息ID参数
2. 检查用户是否有权限访问该消息
3. 获取消息详细内容
4. 自动标记消息为已读（记录首次和最后阅读时间）
5. 返回消息详情

### 3. 获取用户消息统计

```java
UserMessageReadCountDto getUserMessageReadCount()
```

#### 功能描述
获取当前用户的消息统计信息，包括总数、已读数量和未读数量。

#### 参数说明
无参数，自动获取当前登录用户的统计信息。

#### 返回值
- `UserMessageReadCountDto`: 统计信息对象
  - `total`: 消息总数
  - `read`: 已读消息数量
  - `unread`: 未读消息数量

#### 使用示例
```java
// 获取消息统计
UserMessageReadCountDto statistics = messageQueryService.getUserMessageReadCount();
System.out.println("总消息数: " + statistics.getTotal());
System.out.println("已读数量: " + statistics.getRead());
System.out.println("未读数量: " + statistics.getUnread());
```

#### 性能优化
- 使用索引优化统计查询
- 数据一致性保护（确保未读数量不为负）
- 异常处理和日志记录

## 错误处理

### 常见异常
1. `ServiceException(PARAM_IS_INVALID)`: 参数验证失败
2. `ServiceException(RESULT_IS_NULL)`: 消息不存在或无权限访问
3. `ServiceException(SYSTEM_INNER_ERROR)`: 系统内部错误

### 错误码说明
- `PARAM_IS_INVALID`: 请求参数无效
- `RESULT_IS_NULL`: 查询结果为空
- `SYSTEM_INNER_ERROR`: 系统内部错误

## 性能建议

### 1. 查询优化
- 合理设置分页大小（建议20-50条）
- 避免深度分页（使用游标分页）
- 使用索引覆盖查询

### 2. 缓存策略
- 缓存用户消息统计信息
- 缓存热点消息内容
- 使用Redis缓存已读状态

### 3. 批量操作
- 批量获取已读状态
- 批量标记已读
- 避免循环调用

## 最佳实践

### 1. 参数验证
```java
// 总是验证输入参数
if (request == null || request.getPageNum() == null) {
    throw new ServiceException(ResultCode.PARAM_IS_INVALID, "查询参数不能为空");
}
```

### 2. 异常处理
```java
try {
    return messageQueryService.listUserMessageList(request);
} catch (ServiceException e) {
    log.error("查询用户消息列表失败", e);
    // 处理业务异常
} catch (Exception e) {
    log.error("系统异常", e);
    // 处理系统异常
}
```

### 3. 日志记录
```java
log.debug("开始查询用户消息列表，用户ID: {}, 查询条件: {}", userId, request);
```

## 版本历史

### v2.0 (当前版本)
- 重构查询逻辑，提升性能
- 增强参数验证和错误处理
- 优化SQL查询和索引使用
- 完善日志记录和监控

### v1.0
- 基础消息查询功能
- 简单的已读状态管理
