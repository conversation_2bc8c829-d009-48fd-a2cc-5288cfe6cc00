# MyBatis 参数映射问题修复说明

## 问题描述

在运行时遇到以下错误：
```
org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: org.apache.ibatis.binding.BindingException: Parameter 'messageIds' not found. Available parameters are [request, arg0, messageId, param3, userId, param4, param1, param2]
```

## 问题原因

MyBatis Mapper接口中的参数名与XML文件中使用的参数名不一致：

### 原始问题代码：

**SysMessageMapper.java:**
```java
Page<SysMessage> pageUserMessageIsRead(Page<SysMessage> page,
                                       @Param("userId") Long userId,
                                       @Param("request") UserMessageListQueryRequest request,
                                       @Param("messageId") List<Long> messageIds);  // 注意这里是 messageId
```

**SysMessageMapper.xml:**
```xml
<if test="messageIds != null and messageIds.size() > 0">  <!-- 但XML中使用的是 messageIds -->
    AND sm.id IN
    <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
        #{messageId}
    </foreach>
</if>
```

## 修复方案

### 1. 统一参数名称

将Mapper接口中的参数名统一为 `messageIds`：

**修复后的 SysMessageMapper.java:**
```java
/**
 * 根据用户ID分页查询系统消息表信息（已读消息）
 *
 * @param page       分页对象
 * @param userId     用户ID
 * @param request    查询参数
 * @param messageIds 已读消息ID列表
 * @return 列表
 */
Page<SysMessage> pageUserMessageIsRead(Page<SysMessage> page,
                                       @Param("userId") Long userId,
                                       @Param("request") UserMessageListQueryRequest request,
                                       @Param("messageIds") List<Long> messageIds);

/**
 * 根据用户ID分页查询未读的系统消息表信息
 *
 * @param page       分页对象
 * @param userId     用户ID
 * @param request    查询参数
 * @param messageIds 已读消息ID列表（用于排除）
 * @return 列表
 */
Page<SysMessage> pageUserMessageIsUnRead(Page<SysMessage> page,
                                         @Param("userId") Long userId,
                                         @Param("request") UserMessageListQueryRequest request,
                                         @Param("messageIds") List<Long> messageIds);
```

### 2. XML文件保持不变

XML文件中的参数名已经是正确的 `messageIds`，无需修改：

**SysMessageMapper.xml:**
```xml
<!-- 分页查询用户已读消息 -->
<select id="pageUserMessageIsRead" resultMap="BaseResultMap">
    ...
    <if test="messageIds != null and messageIds.size() > 0">
        AND sm.id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </if>
    ...
</select>

<!-- 分页查询用户未读消息 -->
<select id="pageUserMessageIsUnRead" resultType="cn.zhangchuangla.system.message.model.entity.SysMessage">
    ...
    <if test="messageIds != null and messageIds.size() > 0">
        AND sm.id NOT IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </if>
    ...
</select>
```

## 验证修复

### 1. 编译检查
```bash
mvn compile
```

### 2. 参数映射验证
确保以下参数映射正确：
- `@Param("userId")` → `#{userId}`
- `@Param("request")` → `#{request.xxx}`
- `@Param("messageIds")` → `#{messageIds}` 和 `collection="messageIds"`

### 3. 运行时测试
```java
// 测试已读消息查询
List<Long> readMessageIds = Arrays.asList(1L, 2L, 3L);
Page<SysMessage> readMessages = sysMessageMapper.pageUserMessageIsRead(
    new Page<>(1, 10), userId, request, readMessageIds);

// 测试未读消息查询
Page<SysMessage> unreadMessages = sysMessageMapper.pageUserMessageIsUnRead(
    new Page<>(1, 10), userId, request, readMessageIds);
```

## 最佳实践

### 1. 参数命名一致性
- Mapper接口中的 `@Param` 注解值应与XML中的参数名完全一致
- 建议使用有意义的复数形式命名集合参数（如 `messageIds` 而不是 `messageId`）

### 2. 参数验证
```xml
<if test="messageIds != null and messageIds.size() > 0">
    <!-- 只有在集合非空时才执行查询条件 -->
</if>
```

### 3. 集合遍历
```xml
<foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
    #{messageId}
</foreach>
```

## 注意事项

1. **参数名大小写敏感**：确保 `@Param` 注解中的名称与XML中完全一致
2. **集合判空**：使用 `collection.size() > 0` 而不是 `!collection.isEmpty()`
3. **SQL注入防护**：使用 `#{}` 而不是 `${}` 来防止SQL注入
4. **性能考虑**：对于大量ID的IN查询，考虑分批处理或使用临时表

## 修复完成的文件

### 1. Mapper接口层
- ✅ `SysMessageMapper.java` - 统一参数名为 `messageIds`
- ✅ `SysMessageMapper.xml` - SQL映射文件（无需修改）

### 2. Service接口层
- ✅ `SysMessageService.java` - 统一接口参数名为 `messageIds`
- ✅ `SysMessageServiceImpl.java` - 统一实现类参数名为 `messageIds`

### 3. 业务逻辑层
- ✅ `MessageQueryServiceImpl.java` - 调用方法无需修改（已正确）

## 修复验证清单

### ✅ 参数名一致性检查
- [x] SysMessageMapper.pageUserMessageIsRead: `@Param("messageIds")`
- [x] SysMessageMapper.pageUserMessageIsUnRead: `@Param("messageIds")`
- [x] SysMessageService.pageUserMessageIsRead: `List<Long> messageIds`
- [x] SysMessageService.pageUserMessageIsUnRead: `List<Long> messageIds`
- [x] SysMessageServiceImpl.pageUserMessageIsRead: `List<Long> messageIds`
- [x] SysMessageServiceImpl.pageUserMessageIsUnRead: `List<Long> messageIds`

### ✅ XML映射检查
- [x] pageUserMessageIsRead: `collection="messageIds"`
- [x] pageUserMessageIsUnRead: `collection="messageIds"`
- [x] 空集合判断: `messageIds != null and messageIds.size() > 0`

### ✅ 方法调用检查
- [x] MessageQueryServiceImpl 中的方法调用参数正确

## 测试建议

运行以下测试用例验证修复效果：

```java
@Test
public void testPageUserMessageIsRead() {
    List<Long> messageIds = Arrays.asList(1L, 2L, 3L);
    Page<SysMessage> page = new Page<>(1, 10);
    UserMessageListQueryRequest request = new UserMessageListQueryRequest();

    // 应该不再抛出参数映射异常
    Page<SysMessage> result = sysMessageService.pageUserMessageIsRead(page, 1L, request, messageIds);
    assertNotNull(result);
}

@Test
public void testPageUserMessageIsUnRead() {
    List<Long> messageIds = Arrays.asList(1L, 2L, 3L);
    Page<SysMessage> page = new Page<>(1, 10);
    UserMessageListQueryRequest request = new UserMessageListQueryRequest();

    // 应该不再抛出参数映射异常
    Page<SysMessage> result = sysMessageService.pageUserMessageIsUnRead(page, 1L, request, messageIds);
    assertNotNull(result);
}
```

修复完成后，MyBatis参数映射错误应该得到解决。所有相关文件的参数名现在都统一使用 `messageIds`。
