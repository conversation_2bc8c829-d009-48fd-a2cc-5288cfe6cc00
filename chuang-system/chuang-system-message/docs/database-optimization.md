# 消息系统数据库优化建议

## 索引优化建议

### 1. sys_message 表索引

```sql
-- 基础查询索引
CREATE INDEX idx_sys_message_deleted_type ON sys_message(is_deleted, type);
CREATE INDEX idx_sys_message_deleted_level ON sys_message(is_deleted, level);
CREATE INDEX idx_sys_message_deleted_create_time ON sys_message(is_deleted, create_time DESC);

-- 发送者查询索引
CREATE INDEX idx_sys_message_sender ON sys_message(sender_id, is_deleted);
CREATE INDEX idx_sys_message_sender_name ON sys_message(sender_name, is_deleted);

-- 目标类型查询索引
CREATE INDEX idx_sys_message_target_type ON sys_message(target_type, is_deleted);

-- 复合查询索引（根据常用查询组合）
CREATE INDEX idx_sys_message_query ON sys_message(is_deleted, target_type, type, level, create_time DESC);
```

### 2. sys_user_message 表索引

```sql
-- 用户消息关联查询索引
CREATE INDEX idx_user_message_user_id ON sys_user_message(user_id, message_id);
CREATE INDEX idx_user_message_message_id ON sys_user_message(message_id);

-- 部门消息查询索引
CREATE INDEX idx_user_message_dept ON sys_user_message(dept_id, message_id);

-- 角色消息查询索引
CREATE INDEX idx_user_message_role ON sys_user_message(role_id, message_id);

-- 已读状态查询索引
CREATE INDEX idx_user_message_read_status ON sys_user_message(user_id, is_read);
```

### 3. user_message_ext 表索引

```sql
-- 用户已读状态查询索引（最重要）
CREATE INDEX idx_user_message_ext_user_read ON user_message_ext(user_id, is_read, message_id);

-- 消息已读状态查询索引
CREATE INDEX idx_user_message_ext_message ON user_message_ext(message_id, user_id);

-- 已读时间查询索引
CREATE INDEX idx_user_message_ext_read_time ON user_message_ext(user_id, first_read_time);
CREATE INDEX idx_user_message_ext_last_read ON user_message_ext(user_id, last_read_time);
```

## 查询优化建议

### 1. 分页查询优化

- 使用覆盖索引减少回表查询
- 对于大数据量分页，考虑使用游标分页替代OFFSET
- 限制单页最大数据量（建议不超过100条）

### 2. 已读状态查询优化

- 批量查询已读状态，避免N+1查询问题
- 使用IN查询替代多次单条查询
- 考虑使用Redis缓存热点用户的已读状态

### 3. 统计查询优化

- 对于消息统计，考虑使用定时任务预计算
- 使用COUNT(1)替代COUNT(*)
- 避免在大表上进行实时统计

## 缓存策略建议

### 1. Redis缓存设计

```
# 用户消息统计缓存
user:message:count:{userId} -> {total:100, read:80, unread:20}
TTL: 300秒

# 用户已读消息ID集合缓存
user:message:read:{userId} -> Set<messageId>
TTL: 600秒

# 热点消息内容缓存
message:content:{messageId} -> MessageContent
TTL: 1800秒
```

### 2. 缓存更新策略

- 写入时更新缓存（Cache Aside模式）
- 设置合理的TTL避免缓存雪崩
- 使用分布式锁避免缓存击穿

## 性能监控建议

### 1. 慢查询监控

- 监控执行时间超过100ms的查询
- 定期分析慢查询日志
- 优化高频慢查询

### 2. 索引使用监控

- 监控索引命中率
- 识别未使用的索引
- 定期维护索引统计信息

### 3. 连接池监控

- 监控数据库连接池使用情况
- 合理配置连接池大小
- 避免连接泄漏

## 数据归档建议

### 1. 历史数据处理

- 定期归档超过6个月的消息数据
- 保留必要的统计信息
- 使用分区表提高查询性能

### 2. 清理策略

- 定期清理已删除的消息记录
- 清理无效的用户消息关联记录
- 压缩历史数据表

## 并发控制建议

### 1. 乐观锁

- 在更新已读状态时使用版本号控制
- 避免重复标记已读的竞态条件

### 2. 分布式锁

- 对于批量操作使用分布式锁
- 避免同一用户并发操作冲突

## 容量规划建议

### 1. 存储容量

- 预估消息增长量
- 规划存储空间
- 考虑数据压缩策略

### 2. 性能容量

- 压测确定系统性能上限
- 制定扩容策略
- 监控关键性能指标
