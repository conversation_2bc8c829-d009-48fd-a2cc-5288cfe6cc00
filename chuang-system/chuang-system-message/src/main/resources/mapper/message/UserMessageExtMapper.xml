<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.message.mapper.UserMessageExtMapper">

    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.message.model.entity.SysUserMessageExt">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="messageId" column="message_id"/>
        <result property="isRead" column="is_read"/>
        <result property="firstReadTime" column="first_read_time"/>
        <result property="lastReadTime" column="last_read_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getReadMessageCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM user_message_ext ume
                 JOIN sys_message sm ON ume.message_id = sm.id
        WHERE ume.user_id = #{userId}
          AND ume.is_read = 1
          AND sm.is_deleted = 0
    </select>
</mapper>
