<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.message.mapper.SysUserMessageMapper">

    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.message.model.entity.SysUserMessage">
        <id property="id" column="id"/>
        <result property="messageId" column="message_id"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="isRead" column="is_read"/>
        <result property="readTime" column="read_time"/>
        <result property="isStarred" column="is_starred"/>
        <result property="starredTime" column="starred_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>
