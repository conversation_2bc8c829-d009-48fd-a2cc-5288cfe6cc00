<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.zhangchuangla</groupId>
        <artifactId>chuang-system</artifactId>
        <version>1.0.0</version>
    </parent>

    <description>
        站内信息模块，用于系统或者管理员推送站内消息
    </description>

    <artifactId>chuang-system-message</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- MQ 消息队列模块 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-common-mq</artifactId>
        </dependency>

        <!-- WebSocket 模块 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-common-websocket</artifactId>
        </dependency>

        <!-- 系统核心模块 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-system-core</artifactId>
        </dependency>

        <!-- MyBatis-Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot 验证 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>


</project>
