package cn.zhangchuangla.system.core.loader;

import cn.zhangchuangla.common.core.entity.Option;
import cn.zhangchuangla.common.core.loader.DataLoader;
import cn.zhangchuangla.common.redis.constant.RedisConstants;
import cn.zhangchuangla.common.redis.core.RedisCache;
import cn.zhangchuangla.system.core.model.entity.SysDictData;
import cn.zhangchuangla.system.core.model.entity.SysDictType;
import cn.zhangchuangla.system.core.service.SysDictDataService;
import cn.zhangchuangla.system.core.service.SysDictTypeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 字典加载器，在启动项目的时候统一将字典数据加载到缓存中
 * 本次操作为异步，所以不会影响启动速度
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SysDictLoader implements DataLoader {

    private final SysDictTypeService sysDictTypeService;
    private final SysDictDataService sysDictDataService;
    private final RedisCache redisCache;

    @Override
    public String getName() {
        return "系统字典加载器";
    }

    @Override
    public int getOrder() {
        // 优先级适中
        return 20;
    }

    @Override
    public boolean load() {
        try {
            log.info("开始加载系统字典数据到缓存...");

            // 1. 查询所有启用的字典类型（状态：0=启用，1=禁用）
            LambdaQueryWrapper<SysDictType> dictTypeLambdaQueryWrapper = new LambdaQueryWrapper<SysDictType>()
                    .eq(SysDictType::getStatus, 0);
            List<SysDictType> dictTypes = sysDictTypeService.list(dictTypeLambdaQueryWrapper);

            if (dictTypes.isEmpty()) {
                log.info("没有找到启用的字典类型，跳过字典缓存加载");
                return true;
            }

            int successCount = 0;
            int failCount = 0;

            // 2. 为每个字典类型加载字典数据到缓存
            for (SysDictType dictType : dictTypes) {
                try {
                    // 查询该字典类型下所有启用的字典数据（状态：1=启用，0=禁用）
                    LambdaQueryWrapper<SysDictData> dictDataWrapper = new LambdaQueryWrapper<SysDictData>()
                            .eq(SysDictData::getDictType, dictType.getDictType())
                            .eq(SysDictData::getStatus, 1)
                            // 按排序字段升序
                            .orderByAsc(SysDictData::getSort);

                    List<SysDictData> dictDataList = sysDictDataService.list(dictDataWrapper);

                    // 转换为Option格式
                    List<Option<String>> options = dictDataList.stream()
                            .map(item -> new Option<>(item.getDictValue(), item.getDictLabel()))
                            .toList();

                    // 3. 将数据放入缓存
                    String cacheKey = String.format(RedisConstants.Dict.DICT_DATA_KEY, dictType.getDictType());
                    redisCache.setCacheObject(cacheKey, options, RedisConstants.Dict.DICT_CACHE_EXPIRE_TIME);

                    successCount++;

                } catch (Exception e) {
                    failCount++;
                    log.error("字典类型 [{}] 缓存失败: {}", dictType.getDictType(), e.getMessage(), e);
                }
            }

            log.info("系统字典数据加载完成，成功: {} 个，失败: {} 个", successCount, failCount);
            return failCount == 0;

        } catch (Exception e) {
            log.error("加载系统字典数据失败", e);
            return false;
        }
    }

    @Override
    public boolean isAsync() {
        // 支持异步加载
        return true;
    }

    @Override
    public boolean allowStartupOnFailure() {
        // 字典加载失败不应阻止项目启动
        return true;
    }
}
