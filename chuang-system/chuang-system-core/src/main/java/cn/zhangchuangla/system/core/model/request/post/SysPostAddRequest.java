package cn.zhangchuangla.system.core.model.request.post;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 岗位表
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "添加岗位请求对象", description = "添加岗位请求对象")
public class SysPostAddRequest {

    /**
     * 岗位编码
     */
    @Schema(description = "岗位编码", example = "1001", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
    private String postCode;

    /**
     * 岗位名称
     */
    @Schema(description = "岗位名称", example = "开发工程师", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "岗位名称不能为空")
    private String postName;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1", type = "integer")
    private Integer sort;

    /**
     * 状态(0-正常,1-停用)
     */
    @Schema(description = "状态(0-正常,1-停用)", example = "0", type = "integer")
    private Integer status;


}
