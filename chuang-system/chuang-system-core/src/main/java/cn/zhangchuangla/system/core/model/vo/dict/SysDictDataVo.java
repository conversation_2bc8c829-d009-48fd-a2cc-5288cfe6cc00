package cn.zhangchuangla.system.core.model.vo.dict;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 字典数据视图对象
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "字典数据视图对象", description = "字典数据视图对象")
public class SysDictDataVo {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID", type = "integer", format = "int64")
    private Long id;

    /**
     * 字典类型
     */
    @Schema(description = "字典类型", type = "string")
    private String dictType;

    /**
     * 字典标签
     */
    @Schema(description = "字典标签", type = "string")
    private String dictLabel;

    /**
     * 字典值
     */
    @Schema(description = "字典值", type = "string")
    private String dictValue;

    /**
     * 是否默认：1是，0否
     */
    @Schema(description = "是否默认：1是，0否", type = "integer")
    private Integer isDefault;

    /**
     * 排序
     */
    @Schema(description = "排序", type = "integer")
    private Integer sort;

    /**
     * 状态：1启用，0禁用
     */
    @Schema(description = "状态：1启用，0禁用", type = "integer")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注", type = "string")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人", type = "string")
    private String createBy;

    /**
     * 更新人
     */
    @Schema(description = "更新人", type = "string")
    private String updateBy;
}
