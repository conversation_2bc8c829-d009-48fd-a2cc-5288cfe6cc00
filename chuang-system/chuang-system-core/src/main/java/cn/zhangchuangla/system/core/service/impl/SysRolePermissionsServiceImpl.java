package cn.zhangchuangla.system.core.service.impl;

import cn.zhangchuangla.system.core.mapper.SysRolePermissionsMapper;
import cn.zhangchuangla.system.core.model.entity.SysRolePermissions;
import cn.zhangchuangla.system.core.service.SysRolePermissionsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 角色权限服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SysRolePermissionsServiceImpl extends ServiceImpl<SysRolePermissionsMapper, SysRolePermissions>
        implements SysRolePermissionsService {


}




