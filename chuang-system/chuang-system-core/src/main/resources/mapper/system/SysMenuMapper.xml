<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.core.mapper.SysMenuMapper">


    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.core.model.entity.SysMenu">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="title" column="title"/>
        <result property="path" column="path"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="parentId" column="parent_id"/>
        <result property="activePath" column="active_path"/>
        <result property="activeIcon" column="active_icon"/>
        <result property="icon" column="icon"/>
        <result property="component" column="component"/>
        <result property="permission" column="permission"/>
        <result property="badgeType" column="badge_type"/>
        <result property="badge" column="badge"/>
        <result property="badgeVariants" column="badge_variants"/>
        <result property="keepAlive" column="keep_alive"/>
        <result property="affixTab" column="affix_tab"/>
        <result property="hideInMenu" column="hide_in_menu"/>
        <result property="hideChildrenInMenu" column="hide_children_in_menu"/>
        <result property="hideInBreadcrumb" column="hide_in_breadcrumb"/>
        <result property="hideInTab" column="hide_in_tab"/>
        <result property="link" column="link"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="listSysMenuByRoleName" resultMap="BaseResultMap">
        select distinct sm.*
        from sys_menu sm
                 join sys_role_menu srm on sm.id = srm.menu_id
                 join sys_role sr on srm.role_id = sr.id
        where sr.role_name in #{roleName}
          and sm.status = 0
    </select>

    <select id="getPermissionByRole" resultType="java.lang.String">
        SELECT DISTINCT sm.permission
        FROM sys_menu sm
        INNER JOIN sys_role_menu srm ON sm.id = srm.menu_id
        INNER JOIN sys_role sr ON srm.role_id = sr.id
        WHERE sr.role_key IN
        <foreach collection="roleSet" item="role" open="(" separator="," close=")">
            #{role}
        </foreach>
        AND sm.permission IS NOT NULL
        AND sm.permission != ''
        AND sm.status = 0
    </select>


</mapper>
