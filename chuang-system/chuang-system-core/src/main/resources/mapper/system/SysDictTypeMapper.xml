<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.core.mapper.SysDictTypeMapper">

    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.core.model.entity.SysDictType">
        <id property="id" column="id"/>
        <result property="dictType" column="dict_type"/>
        <result property="dictName" column="dict_name"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="listDictType" resultMap="BaseResultMap">
        SELECT
        id,
        dict_type,
        dict_name,
        status,
        remark,
        create_time,
        update_time,
        create_by,
        update_by
        FROM sys_dict_type
        <where>
            <if test="request != null">
                <if test="request.dictType != null and request.dictType != ''">
                    AND dict_type LIKE CONCAT('%', #{request.dictType}, '%')
                </if>
                <if test="request.dictName != null and request.dictName != ''">
                    AND dict_name LIKE CONCAT('%', #{request.dictName}, '%')
                </if>
                <if test="request.status != null">
                    AND status = #{request.status}
                </if>
                <if test="request.startTime != null">
                    AND date(create_time) >= #{request.startTime}
                </if>
                <if test="request.endTime != null">
                    AND date(create_time) &lt;= #{request.endTime}
                </if>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
