<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.core.mapper.SysOperationLogMapper">

    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.core.model.entity.SysOperationLog">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="module" column="module"/>
        <result property="requestParams" column="request_params"/>
        <result property="operationStatus" column="operation_status"/>
        <result property="operationIp" column="operation_ip"/>
        <result property="operationRegion" column="operation_region"/>
        <result property="responseResult" column="response_result"/>
        <result property="operationType" column="operation_type"/>
        <result property="requestUrl" column="request_url"/>
        <result property="methodName" column="method_name"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="costTime" column="cost_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <update id="cleanLoginLog">
        truncate table sys_operation_log
    </update>

    <select id="listOperationLog" resultMap="BaseResultMap">
        select id, user_id, user_name, module, operation_status, request_method, operation_ip, operation_region,
        response_result, operation_type, request_url, method_name, request_params, error_msg, cost_time, create_time
        from
        sys_operation_log
        <where>
            <if test="request.id != null">
                and id = #{request.id}
            </if>
            <if test="request.userId != null">
                and user_id = #{request.userId}
            </if>
            <if test="request.userName != null and request.userName != ''">
                and user_name like concat('%', #{request.userName}, '%')
            </if>
            <if test="request.requestMethod != null and request.requestMethod != ''">
                and request_method = #{request.requestMethod}
            </if>
            <if test="request.operationIp != null and request.operationIp != ''">
                and operation_ip like concat('%', #{request.operationIp}, '%')
            </if>
            <if test="request.operationResult != null and request.operationResult != ''">
                and response_result = #{request.operationResult}
            </if>
            <if test="request.module != null and request.module != ''">
                and module like concat('%', #{request.module}, '%')
            </if>
            <if test="request.startTime != null">
                and date(create_time) >= #{request.startTime}
            </if>
            <if test="request.endTime != null">
                and date(create_time) &lt;= #{request.endTime}
            </if>

        </where>
        order by create_time desc
    </select>

</mapper>
