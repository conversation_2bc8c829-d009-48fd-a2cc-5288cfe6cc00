<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.core.mapper.SysUserRoleMapper">

    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.core.model.entity.SysUserRole">
        <id property="userRoleId" column="user_role_id"/>
        <id property="userId" column="user_id"/>
        <id property="roleId" column="role_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <delete id="deleteUserRoleByUserId">
        delete
        from sys_user_role
        where user_id = #{userId}
    </delete>

</mapper>
