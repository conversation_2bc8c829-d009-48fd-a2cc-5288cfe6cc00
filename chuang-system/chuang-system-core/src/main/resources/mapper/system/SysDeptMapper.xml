<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.core.mapper.SysDeptMapper">

    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.core.model.entity.SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="manager" column="manager"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <select id="listDepartment" resultMap="BaseResultMap">
        SELECT
        dept_id, dept_name, parent_id, manager,status, description, create_time, update_time, create_by, update_by,
        remark
        FROM sys_dept
        <where>
            <if test="request != null">
                <if test="request.deptName != null and request.deptName != ''">
                    AND dept_name LIKE concat('%', #{request.deptName}, '%')
                </if>
                <if test="request.status != null">
                    AND status = #{status}
                </if>
                <if test="request.manager != null">
                    AND manager = #{request.manager}
                </if>
                <if test="request.description != null and request.description != ''">
                    AND description LIKE concat('%', #{request.description}, '%')
                </if>
            </if>
        </where>
    </select>

</mapper>
