<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.core.mapper.SysRoleMenuMapper">

    <resultMap id="RoleMenuResultMap" type="cn.zhangchuangla.system.core.model.entity.SysRoleMenu">
        <result property="roleId" column="role_id" jdbcType="BIGINT"/>
        <result property="menuId" column="menu_id" jdbcType="BIGINT"/>
    </resultMap>


    <select id="selectMenuListByRoleId" resultType="java.lang.Long">
        select menu_id
        from sys_role_menu
        where role_id = #{roleId}
    </select>

</mapper>
