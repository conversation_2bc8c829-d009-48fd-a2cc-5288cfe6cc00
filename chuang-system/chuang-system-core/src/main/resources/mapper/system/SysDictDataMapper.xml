<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhangchuangla.system.core.mapper.SysDictDataMapper">

    <resultMap id="BaseResultMap" type="cn.zhangchuangla.system.core.model.entity.SysDictData">
        <id property="id" column="id"/>
        <result property="dictType" column="dict_type"/>
        <result property="dictLabel" column="dict_label"/>
        <result property="dictValue" column="dict_value"/>
        <result property="isDefault" column="is_default"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="listDictData" resultMap="BaseResultMap">
        SELECT
        id,
        dict_type,
        dict_label,
        dict_value,
        is_default,
        sort,
        status,
        remark,
        create_time,
        update_time,
        create_by,
        update_by
        FROM sys_dict_data
        <where>
            <if test="dictType != null and dictType != ''">
                AND dict_type = #{dictType}
            </if>
            <if test="request != null">
                <if test="request.dictLabel != null and request.dictLabel != ''">
                    AND dict_label LIKE CONCAT('%', #{request.dictLabel}, '%')
                </if>
                <if test="request.dictValue != null and request.dictValue != ''">
                    AND dict_value LIKE CONCAT('%', #{request.dictValue}, '%')
                </if>
                <if test="request.status != null">
                    AND status = #{request.status}
                </if>
                <if test="request.startTime != null">
                    AND date(create_time) >= #{request.startTime}
                </if>
                <if test="request.endTime != null">
                    AND date(create_time) &lt;= #{request.endTime}
                </if>
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>

</mapper>
