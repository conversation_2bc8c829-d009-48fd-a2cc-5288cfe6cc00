package cn.zhangchuangla.system.storage.mapper;

import cn.zhangchuangla.system.storage.model.entity.StorageFile;
import cn.zhangchuangla.system.storage.model.request.file.StorageFileQueryRequest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface StorageFileMapper extends BaseMapper<StorageFile> {

    /**
     * 文件列表
     *
     * @param page    分页对象
     * @param request 请求参数
     * @return 结果
     */
    Page<StorageFile> listFileManage(Page<StorageFile> page, @Param("request") StorageFileQueryRequest request);


    /**
     * 回收站文件列表
     *
     * @param page    分页对象
     * @param request 请求参数
     * @return 结果
     */
    Page<StorageFile> listFileTrashManage(Page<StorageFile> page, @Param("request") StorageFileQueryRequest request);

    /**
     * 导出文件列表
     *
     * @param request 请求参数
     * @return 结果
     */
    List<StorageFile> exportListFile(StorageFileQueryRequest request);
}




