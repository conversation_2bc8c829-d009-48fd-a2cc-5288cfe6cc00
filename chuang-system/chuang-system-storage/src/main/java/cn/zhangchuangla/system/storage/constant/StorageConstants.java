package cn.zhangchuangla.system.storage.constant;

import java.util.List;

/**
 * 存储类型常量
 *
 * <AUTHOR>
 * <p>
 * created on 2025/4/3
 */
public class StorageConstants {

    /**
     * 文件上传路径格式
     */
    public static final String FILE_UPLOAD_PATH_FORMAT = "yyyy/MM";

    /**
     * 图片后缀
     */
    public static List<String> imageSuffix = List.of(
            "jpg",
            "jpeg",
            "png",
            "gif",
            "bmp",
            "webp",
            "heic",
            "heif",
            "jfif",
            "raw"
    );


    /**
     * 图片压缩常量
     */
    public interface imageCompression {
        //图片最大宽
        int MAX_WIDTH = 1920;
        //图片最大高
        int MAX_HEIGHT = 1080;
        //图片压缩率
        float QUALITY = 0.8f;
    }

    /**
     * 文件目录常量
     */
    public interface dirName {

        /**
         * 图片目录
         */
        String IMAGE = "image";

        /**
         * 文件目录
         */
        String FILE = "file";

        /**
         * 回收站文件夹
         */
        String TRASH = "trash";

        /**
         * 原图目录
         */
        String ORIGINAL = "original";

        /**
         * 预览目录
         */
        String PREVIEW = "preview";

        /**
         * 资源目录
         */
        String RESOURCE = "resource";
    }


    /**
     * 存储类型常量
     */
    public interface StorageType {
        /**
         * 本地存储
         */
        String LOCAL = "local";

        /**
         * 阿里云OSS存储
         */
        String ALIYUN_OSS = "aliyun_oss";

        /**
         * 腾讯云COS存储
         */
        String TENCENT_COS = "tencent_cos";

        /**
         * MinIO存储
         */
        String MINIO = "minio";

        /**
         * Amazon S3存储
         */
        String AMAZON_S3 = "amazon_s3";
    }

    /**
     * 数据验证常量
     */
    public interface dataVerifyConstants {
        /**
         * 文件上传主配置
         */
        boolean PRIMARY = true;

        /**
         * 在回收站
         */
        Integer IN_TRASH = 1;

        /**
         * 不在回收站
         */
        Integer NOT_IN_TRASH = 0;
    }


    /**
     * Spring Bean名称
     */
    public interface springBeanName {
        /**
         * 本地存储服务名称
         */
        String LOCAL_STORAGE_SERVICE = "localStorageService";

        /**
         * 阿里云OSS存储服务名称
         */
        String ALIYUN_OSS_STORAGE_SERVICE = "aliyunOssStorageService";

        /**
         * MinIO存储服务名称
         */
        String MINIO_STORAGE_SERVICE = "minioStorageService";

        /**
         * 腾讯云COS存储服务名称
         */
        String TENCENT_COS_STORAGE_SERVICE = "tencentCosStorageService";

        /**
         * 亚马逊S3存储服务名称
         */
        String AMAZON_S3_STORAGE_SERVICE = "amazonS3StorageService";
    }


}
