package cn.zhangchuangla.system.monitor.request;

import cn.zhangchuangla.common.core.entity.base.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 端点统计查询请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "端点统计查询请求")
public class EndpointStatsQueryRequest extends BasePageRequest {

    /**
     * HTTP方法
     */
    @Schema(description = "HTTP方法", example = "GET")
    private String method;

    /**
     * URI路径（支持模糊查询）
     */
    @Schema(description = "URI路径", example = "/api/users")
    private String uri;

    /**
     * 端点路径（支持模糊查询）
     */
    @Schema(description = "端点路径", example = "GET /api/users")
    private String endpoint;

    /**
     * 最小请求数过滤
     */
    @Schema(description = "最小请求数")
    private Long minRequestCount;

    /**
     * 最大平均响应时间过滤（毫秒）
     */
    @Schema(description = "最大平均响应时间（毫秒）")
    private Double maxAverageResponseTime;

    /**
     * 最小成功率过滤（百分比）
     */
    @Schema(description = "最小成功率（百分比）")
    private Double minSuccessRate;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", allowableValues = {"requestCount", "averageResponseTime", "maxResponseTime", "successRate", "qps"})
    private String sortBy = "requestCount";

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", allowableValues = {"asc", "desc"})
    private String sortDirection = "desc";
}
