app:
  config:
    upload-path: /Users/<USER>/DevelopFileUpload
    enable-trash: true
  name: EchoPro
  version: 0.9.0
#开发环境数据库文件
server:
  port: 8080
spring:
  profiles:
    active: druid,rabbitmq,quartz,monitor,storage #激活配置文件
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  data:
    redis:
      host: **************
      port: 6379
      database: 0
      password: zhangchuang2726
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    tags-sorter: alpha
    operations-sorter: alpha
    display-request-duration: true
    syntax-highlight:
      activated: true
  default-produces-media-type: application/json
  default-consumes-media-type: application/json
  cache:
    disabled: true

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-aliases-package: cn.zhangchuangla.**.entity
  mapper-locations:
    - classpath*:mapper/**/**.xml
  global-config:
    db-config:
      logic-delete-field: isDeleted  # 全局逻辑删除字段名
      logic-delete-value: 1         # 逻辑已删除值
      logic-not-delete-value: 0     # 逻辑未删除值

security:
  secret: zhangchuang2726zhangchuang2726zhangchuang2726
  header: Authorization
  password-config:
    lock-time: 3
    max-retry-count: 5
  session:
    access-token-expire-time: 7200000
    refresh-token-expire-time: 2592000
    max-sessions-per-client:
      pc: 10
      mini-program: -1
      web: -1
      unknown: 10
      mobile: -1
    multi-device: true
    token-prefix: null
