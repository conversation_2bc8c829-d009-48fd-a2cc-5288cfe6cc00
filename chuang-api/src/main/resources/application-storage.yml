# 存储系统配置
storage:
  # 当数据库中未找到主存储配置时，将使用此默认配置。设置为 "local" 表示优先使用本地存储；
  # 若设置为 "minio"，则表示在数据库配置不可用时降级使用 MinIO 配置；若未指定 MinIO 配置，则仍将默认使用本地存储。
  active-type: minio
  # 本地存储配置
  local:
    uploadPath: ${app.config.upload-path}
    file-domain: http://localhost:8080/
    real-delete: false

  # MinIO 配置
  minio:
    endpoint: http://**************:9000
    access-key: SZfViEdywGsEblLuql1I
    secret-key: cnBcWgH6KNKgtMVfhcU5JZ5mD2BTSXM8AOvm5DlH
    bucket-name: develop
    file-domain: http://**************:9000/develop
    real-delete: false

  # 阿里云 OSS 配置
  aliyun-oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    access-key-id: ${ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
    bucket-name: chuang-base1
    file-domain: https://oss.zhangchuangla.cn
    real-delete: false

  # 腾讯云 COS 配置
  tencent-cos:
    region: ap-shanghai
    secret-id: AKIDmY6Rj2ED0Zv3x31oWAi8YotmXAcUO7cu
    secret-key: 72nRyIZfYDer36wyqUvDJBqYDoCQUmuo
    bucket-name: develop-1314815437
    file-domain: https://echopro-1234567890.cos.ap-shanghai.myqcloud.com
    real-delete: false
  # 亚马逊S3
  amazon-s3:
    endpoint: http://**************:9000
    bucket-name: develop
    region: local
    access-key: SZfViEdywGsEblLuql1I
    secret-key: cnBcWgH6KNKgtMVfhcU5JZ5mD2BTSXM8AOvm5DlH
    file-domain: http://**************:9000/develop
    real-delete: false
