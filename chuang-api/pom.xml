<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.zhangchuangla</groupId>
        <artifactId>echo-pro</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>chuang-api</artifactId>
    <version>1.0.0</version>
    <description>API入口模块，包含启动类和对外接口</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 依赖框架模块（包含所有核心功能支持） -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-system-message</artifactId>
        </dependency>
        <!-- 依赖存储模块 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-system-storage</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-system-core</artifactId>
        </dependency>
        <!-- 监控模块 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-system-monitor</artifactId>
        </dependency>
        <!-- 常用工具模块 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-common-excel</artifactId>
        </dependency>
        <!-- 定时任务 -->
        <dependency>
            <groupId>cn.zhangchuangla</groupId>
            <artifactId>chuang-quartz</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <mainClass>cn.zhangchuangla.api.APIApplication</mainClass>
                    <layout>JAR</layout>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
